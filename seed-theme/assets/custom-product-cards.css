/* Custom Product Card Styles for Collection Pages */

/* Product Title Styling */
.custom-product-title {
  background-color: rgba(0, 0, 0, 0) !important;
  box-sizing: border-box !important;
  color: rgb(74, 74, 74) !important;
  cursor: pointer !important;
  display: inline !important;
  font-family: Lato, sans-serif !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  height: auto !important;
  line-height: 24px !important;
  outline-color: rgb(74, 74, 74) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  text-decoration-color: rgb(74, 74, 74) !important;
  text-decoration-line: none !important;
  text-decoration-style: solid !important;
  text-decoration-thickness: auto !important;
  text-size-adjust: 100% !important;
  width: auto !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
}

/* Price Container Layout */
.custom-price-container {
  display: flex !important;
  align-items: baseline !important;
  flex-wrap: wrap !important;
}

/* Sale Price Styling */
.custom-sale-price {
  box-sizing: border-box !important;
  color: rgb(252, 8, 8) !important;
  display: inline-block !important;
  font-family: Lato, sans-serif !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  line-height: 22.4px !important;
  margin-right: 5px !important;
  outline-color: rgb(252, 8, 8) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  text-size-adjust: 100% !important;
  text-transform: uppercase !important;
  unicode-bidi: isolate !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
}

/* Code Text Styling */
.custom-code-text {
  background-color: rgba(230, 245, 252, 0.5) !important;
  box-sizing: border-box !important;
  color: rgb(74, 74, 74) !important;
  display: inline-block !important;
  font-family: Lato, sans-serif !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 21px !important;
  outline-color: rgb(74, 74, 74) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  padding: 0px 1px !important;
  text-size-adjust: 100% !important;
  text-transform: uppercase !important;
  unicode-bidi: isolate !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
  border-radius: 2px !important;
}

/* Old Price (Strikethrough) Styling */
.custom-price-container .old-price {
  color: rgb(147, 143, 156) !important;
  font-family: Lato, sans-serif !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 22.4px !important;
  text-decoration: line-through !important;
  margin-right: 5px !important;
}

/* Hide Sale Labels and View Options Buttons */
.product-card .s1lb .overlay-sale,
.product-card .link-btn,
.product-card .overlay-buy_button {
  display: none !important;
}

/* Hide any hover buttons or overlays */
.product-card:hover .overlay-buy_button,
.product-card:hover .link-btn {
  display: none !important;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .custom-product-title {
    font-size: 18px !important;
    line-height: 22px !important;
  }
  
  .custom-sale-price {
    font-size: 15px !important;
  }
  
  .custom-code-text {
    font-size: 14px !important;
    line-height: 19px !important;
  }
  
  .custom-price-container {
    gap: 3px !important;
  }
}


  #collection {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }


/* Desktop Product Grid Sizing - Reduce to 380x330px max */
@media (min-width: 1001px) {
  #collection::after {
    content: "";
    flex: 0 0 calc(60.3%);
  }
  
  /* Limit product card container sizes for desktop */
  .l4cl li {
    max-width: 380px !important;
  }

  /* Set maximum image dimensions to 380x330px */
  .l4cl figure {
    max-width: 380px !important;
    max-height: 330px !important;
  }

  /* Ensure picture container respects max dimensions */
  .l4cl figure picture {
    max-width: 380px !important;
    max-height: 330px !important;
    /* Override ratio calculation for consistent sizing */
    padding-top: min(calc(var(--ratio) * 100%), 330px) !important;
  }

  /* Ensure images fit within container without distortion */
  .l4cl figure img,
  .l4cl figure picture img {
    max-width: 380px !important;
    max-height: 330px !important;
    object-fit: contain !important;
    object-position: center !important;
  }

  /* For filled/cropped images, maintain aspect ratio */
  .l4cl .filled,
  .l4cl .filled *,
  .l4cl figure img.filled {
    object-fit: cover !important;
    max-width: 380px !important;
    max-height: 330px !important;
  }

  /* Adjust grid widths to accommodate smaller images */
  .l4cl.w25 li,
  .l4cl li.w25 {
    width: 25% !important;
    max-width: 380px !important;
  }

  .l4cl.w33 li,
  .l4cl li.w33 {
    width: 33.33333333% !important;
    max-width: 380px !important;
  }

  .l4cl.w50 li,
  .l4cl li.w50 {
    width: 50% !important;
    max-width: 380px !important;
  }

  /* Ensure no overflow and proper spacing */
  .l4cl {
    overflow: visible !important;
  }

  /* Remove any extra padding that might cause issues */
  .l4cl li {
    box-sizing: border-box !important;
  }
}
/* Desktop Product Grid Sizing - Reduce to 380x330px max */


/* Mobile Product Grid Sizing - Reduce to 345x300px max */
@media (max-width: 1000px) {
  /* Limit product card container sizes for mobile */
  .l4cl li {
    max-width: 345px !important;
  }

  /* Set maximum image dimensions to 345x300px */
  .l4cl figure {
    max-width: 345px !important;
    max-height: 300px !important;
  }

  /* Ensure picture container respects max dimensions */
  .l4cl figure picture {
    max-width: 345px !important;
    max-height: 300px !important;
    /* Override ratio calculation for consistent sizing */
    padding-top: min(calc(var(--ratio) * 100%), 300px) !important;
  }

  /* Ensure images fit within container without distortion */
  .l4cl figure img,
  .l4cl figure picture img {
    max-width: 345px !important;
    max-height: 300px !important;
    object-fit: contain !important;
    object-position: center !important;
  }

  /* For filled/cropped images, maintain aspect ratio */
  .l4cl .filled,
  .l4cl .filled *,
  .l4cl figure img.filled {
    object-fit: cover !important;
    max-width: 345px !important;
    max-height: 300px !important;
  }

  /* Ensure no overflow and proper spacing */
  .l4cl {
    overflow: visible !important;
  }

  /* Remove any extra padding that might cause issues */
  .l4cl li {
    box-sizing: border-box !important;
  }
}

@media (max-width: 480px) {
  .custom-product-title {
    font-size: 16px !important;
    line-height: 20px !important;
  }

  .custom-sale-price {
    font-size: 14px !important;
  }

  .custom-code-text {
    font-size: 13px !important;
    line-height: 18px !important;
  }
}
