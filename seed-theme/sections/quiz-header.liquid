<style>
  /* Full width section styles */
  #shopify-section-{{ section.id }} {
    width: 100vw !important;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    max-width: none !important;
  }

  /* Quiz top bar styles */
  .quiz-top-bar {
    width: 100%;
    background: #061B5D;
    box-sizing: border-box;
    color: rgb(74, 74, 74);
    display: flex;
    flex-wrap: wrap;
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-weight: 700;
    height: 47px;
    justify-content: center;
    letter-spacing: 0.0388889px;
    line-height: 22.4px;
    text-align: center;
    align-items: center;
    padding: 0 30px;
    gap: 60px;
  }

  .quiz-top-bar-item {
    box-sizing: border-box;
    color: rgb(255, 255, 255);
    display: inline-block;
    font-family: Lato, sans-serif;
    font-size: 14px;
    font-weight: 700;
    height: auto;
    letter-spacing: 0.4px;
    line-height: 15px;
    text-align: center;
    margin: 0 15px;
    white-space: nowrap;
    vertical-align: middle;
  }

  /* Very small screens adjustments */
  @media only screen and (max-width: 320px) {
    .quiz-top-bar-item:nth-child(3) {
      display: none;
    }

    /* Maintain height and adjust padding for very small screens */
    .quiz-custom-header {
      height: 56px !important;
      min-height: 56px !important;
      max-height: 56px !important;
    }

    .quiz-header-container {
      padding: 0 15px;
      height: 56px;
    }

    /* Even smaller logo */
    .quiz-custom-logo,
    .quiz-default-logo {
      max-width: 60px;
      max-height: 27px;
    }

    /* Smaller center image */
    .quiz-center-image {
      height: 48px;
      width: 48px;
    }

    /* Smaller close button */
    .quiz-close-button {
      padding: 6px;
    }

    .quiz-close-button svg {
      width: 20px;
      height: 20px;
    }
  }

  /* Quiz header styles */
  .quiz-custom-header {
    width: 100%;
    background: #ffffff;
    border-bottom: 1px solid rgb(241, 241, 242);
    box-sizing: border-box;
    color: rgb(74, 74, 74);
    display: block;
    font-family: Lato, sans-serif;
    font-size: 16px;
    height: 82.5625px;
    z-index: 1000;
    position: relative;
    margin: 0;
    padding: 0;
  }

  .quiz-header-container {
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
    padding: 0 30px 0 60px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    box-sizing: border-box;
  }

  /* Left Logo Section */
  .quiz-header-left {
    flex: 0 0 auto;
    z-index: 2;
  }

  .quiz-logo-link {
    display: inline-block;
    text-decoration: none;
    line-height: 0;
  }

  .quiz-custom-logo,
  .quiz-default-logo {
    max-width: 125px;
    height: auto;
    display: block;
  }

  /* Center Image Section */
  .quiz-header-center {
    position: absolute;
    left: 50%;
    top: 100%;
    transform: translate(-50%, -50%);
    z-index: 2;
  }

  .quiz-center-image {
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: cover;
    height: 80px;
    width: 80px;
    border-radius: 50%;
  }

  /* Right Link Section */
  .quiz-header-right {
    flex: 0 0 auto;
    z-index: 2;
  }

  .quiz-back-link {
    color: rgb(105, 105, 105);
    text-decoration: none;
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-weight: 600;
    transition: color 0.2s ease;
  }

  .quiz-back-link:hover {
    color: rgb(80, 80, 80);
  }

  /* Close button styles */
  .quiz-close-button {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding-top: 10px;
    line-height: 0;
    text-decoration: none;
    transition: opacity 0.2s ease;
  }

  .quiz-close-button:hover {
    opacity: 0.7;
  }

  .quiz-close-button svg {
    display: block;
    width: 24px;
    height: 24px;
  }

  /* Mobile Responsive */
  @media only screen and (max-width: 768px) {
    .quiz-top-bar {
      height: auto;
      min-height: 47px;
      padding: 10px 15px;
      flex-direction: column;
      gap: 5px;
    }

    .quiz-top-bar-item {
      font-size: 12px;
      margin: 0 10px;
    }

    .quiz-custom-header {
      height: auto;
      min-height: 70px;
    }

    .quiz-header-container {
      padding: 15px 30px;
      flex-wrap: wrap;
      gap: 15px;
    }

    .quiz-custom-logo,
    .quiz-default-logo {
      max-width: 100px;
    }

    .quiz-center-image {
      height: 60px;
      width: 60px;
    }

    .quiz-back-link {
      font-size: 14px;
    }
  }

  @media only screen and (max-width: 480px) {
    /* Hide top bar on mobile */
    .quiz-top-bar {
      display: none;
    }

    /* Mobile header with fixed height */
    .quiz-custom-header {
      height: 56px !important;
      min-height: 56px !important;
      max-height: 56px !important;
    }

    /* Mobile header layout */
    .quiz-header-container {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      gap: 0;
      height: 56px;
    }

    /* Left logo - smaller size */
    .quiz-header-left {
      flex: 0 0 auto;
      order: 1;
    }

    .quiz-custom-logo,
    .quiz-default-logo {
      max-width: 67px;
      max-height: 30px;
      width: auto;
      height: auto;
    }

    /* Center image - smaller and positioned normally */
    .quiz-header-center {
      position: relative;
      left: auto;
      top: auto;
      transform: none;
      order: 2;
      flex: 1;
      display: flex;
      justify-content: center;
      top: 27px;
      margin-right: 40px;
    }

    .quiz-center-image {
      height: 56px;
      width: 56px;
    }

    /* Right close button */
    .quiz-header-right {
      flex: 0 0 auto;
      order: 3;
    }

    /* Hide text link, show close button */
    .quiz-back-text {
      display: none;
    }

    .quiz-close-button {
      display: inline-block;
    }
  }

  /* Hide default header on quiz pages */
  body.quiz-page .shopify-section-header,
  body.template-page[class*="quiz"] .shopify-section-header,
  body[class*="quiz"] .shopify-section-header {
    display: none !important;
  }

  /* Hide footer on quiz pages */
  body.quiz-page .shopify-section-footer,
  body.quiz-page .shopify-section-group-footer-group,
  body.template-page[class*="quiz"] .shopify-section-footer,
  body.template-page[class*="quiz"] .shopify-section-group-footer-group,
  body[class*="quiz"] .shopify-section-footer,
  body[class*="quiz"] .shopify-section-group-footer-group {
    display: none !important;
  }

  /* White background for entire quiz page */
  body.quiz-page,
  body.template-page[class*="quiz"],
  body[class*="quiz"] {
    background-color: #ffffff !important;
  }

  /* Ensure main content area also has white background */
  body.quiz-page #content,
  body.template-page[class*="quiz"] #content,
  body[class*="quiz"] #content {
    background-color: #ffffff !important;
  }

  /* Hide any footer-related elements */
  body.quiz-page footer,
  body.quiz-page [class*="footer"],
  body.template-page[class*="quiz"] footer,
  body.template-page[class*="quiz"] [class*="footer"],
  body[class*="quiz"] footer,
  body[class*="quiz"] [class*="footer"] {
    display: none !important;
  }

  /* Ensure page wrapper has white background */
  body.quiz-page #root,
  body.template-page[class*="quiz"] #root,
  body[class*="quiz"] #root {
    background-color: #ffffff !important;
  }
</style>

<!-- Quiz Top Bar -->
{% if section.settings.show_top_bar %}
<div class="quiz-top-bar">
  {% if section.settings.top_bar_item_1 != blank %}
    <span class="quiz-top-bar-item">{{ section.settings.top_bar_item_1 }}</span>
  {% endif %}
  {% if section.settings.top_bar_item_2 != blank %}
    <span class="quiz-top-bar-item">{{ section.settings.top_bar_item_2 }}</span>
  {% endif %}
  {% if section.settings.top_bar_item_3 != blank %}
    <span class="quiz-top-bar-item">{{ section.settings.top_bar_item_3 }}</span>
  {% endif %}
</div>
{% endif %}

<!-- Quiz Main Header -->
<header class="quiz-custom-header" id="quiz-custom-header">
  <div class="quiz-header-container">
    <!-- Left: Customizable Logo -->
    <div class="quiz-header-left">
      <a href="{{ routes.root_url }}" class="quiz-logo-link" aria-label="Go to homepage">
        {% if section.settings.custom_logo != blank %}
          <img 
            src="{{ section.settings.custom_logo | image_url: width: 250 }}" 
            alt="{{ section.settings.custom_logo.alt | default: shop.name | escape }}"
            class="quiz-custom-logo"
            width="125"
            height="auto"
          >
        {% else %}
          <!-- Default SVG Logo -->
          <svg xmlns="http://www.w3.org/2000/svg" width="152" height="70" viewBox="0 0 152 70" fill="none" class="quiz-default-logo">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M126.213 63.086h-18.381c-.894 0-1.605.753-1.605 1.698 0 1.006.711 1.76 1.605 1.76h18.381c.894 0 1.604-.754 1.604-1.76 0-.945-.71-1.698-1.604-1.698M45.632 34.568h-9.486c-.89 0-1.601.712-1.601 1.661 0 .89.71 1.602 1.6 1.602h9.487a5.52 5.52 0 0 1 5.516 5.576c0 2.966-2.313 5.516-5.516 5.516h-9.486c-.89 0-1.601.713-1.601 1.66v14.3c0 .947.71 1.66 1.6 1.66.95 0 1.66-.713 1.66-1.66V52.187h7.768c5.633 0 8.836-4.569 8.836-8.78 0-4.865-3.974-8.84-8.777-8.84m21.01 3.264h9.484c.888 0 1.6-.711 1.6-1.603 0-.948-.712-1.66-1.6-1.66h-9.484c-4.804 0-8.776 3.975-8.776 8.84 0 4.213 3.202 8.78 8.832 8.78h2.282c3.185.017 5.483 2.558 5.483 5.515a5.52 5.52 0 0 1-5.514 5.577h-9.485c-.89 0-1.6.711-1.6 1.603 0 .947.71 1.66 1.6 1.66h9.484c4.803 0 8.775-3.976 8.775-8.84 0-4.212-3.201-8.781-8.833-8.781h-1.495l.002.004h-.755c-3.204 0-5.517-2.552-5.517-5.518a5.52 5.52 0 0 1 5.517-5.577m76.582-3.263h-9.486c-.891 0-1.601.712-1.601 1.661 0 .89.71 1.602 1.601 1.602h9.486a5.52 5.52 0 0 1 5.515 5.576c0 2.966-2.312 5.516-5.515 5.516h-9.486c-.891 0-1.601.713-1.601 1.66v14.3c0 .947.71 1.66 1.601 1.66.949 0 1.659-.713 1.659-1.66V52.187h7.768c5.633 0 8.835-4.569 8.835-8.78 0-4.865-3.973-8.84-8.776-8.84m-42.056.001H82.786c-.894 0-1.604.71-1.604 1.602 0 .95.71 1.658 1.604 1.658h7.558v27.113c0 .892.711 1.602 1.66 1.602.894 0 1.606-.71 1.606-1.602V37.828h7.558c.893 0 1.605-.709 1.605-1.658 0-.892-.712-1.602-1.605-1.602m25.045 0h-18.381c-.894 0-1.605.753-1.605 1.699 0 1.006.711 1.758 1.605 1.758h18.381c.894 0 1.604-.752 1.604-1.758 0-.946-.71-1.7-1.604-1.7m0 13.828h-18.381c-.894 0-1.605.752-1.605 1.698 0 1.005.711 1.759 1.605 1.759h18.381c.894 0 1.604-.754 1.604-1.76 0-.944-.71-1.697-1.604-1.697" fill="#00334C"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M4.206 28.232c-.553-2.777-1.054-6.723-1.036-9.51.033-4.655.61-10.423 4.834-13.735 3.313-2.595 9.015-2.263 12.202.712 4.527 4.223 4.434 14.342 3.564 20.08a1.596 1.596 0 0 0 1.326 1.82 1.58 1.58 0 0 0 1.806-1.337c1.036-6.823.889-17.836-4.54-22.904C18.046-.67 10.58-1.076 6.057 2.47.7 6.668.035 13.743 0 18.7c-.02 3.005.502 7.17 1.097 10.16a1.59 1.59 0 0 0 1.865 1.25 1.595 1.595 0 0 0 1.243-1.878" fill="#4A9EDA"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M27.003 47.342c-1.006-2.31-1.957-4.49-2.403-6.818-.544-2.834.055-5.513.713-7.76.248-.848-.223-1.74-1.054-1.995a1.566 1.566 0 0 0-1.954 1.077c-.768 2.622-1.461 5.777-.786 9.295.516 2.683 1.583 5.134 2.616 7.504 1.007 2.31 1.958 4.494 2.407 6.828 1.057 5.484-2.001 8.761-4.94 9.901-3.286 1.272-7.79.487-10.454-4.14-2.108-3.66-2.108-8.166-2.108-12.522 0-.723 0-1.44-.01-2.15-.05-3.849-.716-7.472-2.159-11.746a1.56 1.56 0 0 0-1.997-.992c-.819.288-1.254 1.2-.971 2.037 1.33 3.937 1.943 7.25 1.989 10.742q.01 1.047.008 2.109c0 4.785 0 9.734 2.543 14.148 2.583 4.486 6.497 6.276 10.17 6.276 1.436 0 2.834-.272 4.103-.764 4.404-1.71 8.248-6.551 6.906-13.519-.518-2.687-1.586-5.14-2.619-7.511" fill="#00334C"/>
          </svg>
        {% endif %}
      </a>
    </div>

    <!-- Center: Customizable Image -->
    <div class="quiz-header-center">
      <div class="quiz-center-image" 
           style="background-image: url('{{ section.settings.center_image_url | default: 'https://dcpsvjmu5hpqr.cloudfront.net/images/2020-03/0bc35ca792c6f3d66bab7f35.webp' }}');">
      </div>
    </div>

    <!-- Right: Back to Home Link / Close Button -->
    <div class="quiz-header-right">
      <a href="{{ routes.root_url }}" class="quiz-back-link quiz-back-text">
        {{ section.settings.back_text | default: 'Back to home' }}
      </a>
      <a href="{{ routes.root_url }}" class="quiz-close-button" aria-label="Close">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M6.293 16.293a1 1 0 1 0 1.414 1.414zm6.414-3.586a1 1 0 0 0-1.414-1.414zm-1.414-1.414a1 1 0 0 0 1.414 1.414zm6.414-3.586a1 1 0 0 0-1.414-1.414zm-5 3.586a1 1 0 0 0-1.414 1.414zm3.586 6.414a1 1 0 0 0 1.414-1.414zm-5-5a1 1 0 0 0 1.414-1.414zM7.707 6.293a1 1 0 0 0-1.414 1.414zm0 11.414 5-5-1.414-1.414-5 5zm5-5 5-5-1.414-1.414-5 5zm-1.414 0 5 5 1.414-1.414-5-5zm1.414-1.414-5-5-1.414 1.414 5 5z" fill="#4A4A4A"/>
        </svg>
      </a>
    </div>
  </div>
</header>

{% schema %}
{
  "name": "Quiz Header",
  "class": "quiz-header-section",
  "settings": [
    {
      "type": "header",
      "content": "Top Bar Settings"
    },
    {
      "type": "checkbox",
      "id": "show_top_bar",
      "label": "Show Top Bar",
      "default": true
    },
    {
      "type": "text",
      "id": "top_bar_item_1",
      "label": "Top Bar Item 1",
      "default": "180-day money-back guarantee"
    },
    {
      "type": "text",
      "id": "top_bar_item_2",
      "label": "Top Bar Item 2",
      "default": "Free shipping"
    },
    {
      "type": "text",
      "id": "top_bar_item_3",
      "label": "Top Bar Item 3",
      "default": "FSA/HSA eligible"
    },
    {
      "type": "header",
      "content": "Logo Settings"
    },
    {
      "type": "image_picker",
      "id": "custom_logo",
      "label": "Custom Logo",
      "info": "Upload a custom logo. If not provided, default SVG logo will be used."
    },
    {
      "type": "header",
      "content": "Center Image Settings"
    },
    {
      "type": "text",
      "id": "center_image_url",
      "label": "Center Image URL",
      "default": "https://dcpsvjmu5hpqr.cloudfront.net/images/2020-03/0bc35ca792c6f3d66bab7f35.webp",
      "info": "URL for the center circular image"
    },
    {
      "type": "header",
      "content": "Text Settings"
    },
    {
      "type": "text",
      "id": "back_text",
      "label": "Back Link Text",
      "default": "Back to home"
    }
  ],
  "presets": [
    {
      "name": "Quiz Header"
    }
  ]
}
{% endschema %}
